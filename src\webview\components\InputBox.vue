<template>
  <div class="input-container">
    <!-- 输入区域 -->
    <div class="textarea-container">
      <div class="textarea-wrapper">
        <textarea
          ref="textareaRef"
          v-model="inputValue"
          @keydown="handleKeydown"
          @input="adjustHeight"
          :disabled="isLoading"
          :placeholder="isLoading ? '正在处理中...' : '输入您的问题...'"
          class="input-field"
          rows="1"
        />
        
        <!-- 控制栏 -->
        <div class="input-controls">
          <!-- 左侧控制按钮 -->
          <div class="left-controls">
            <!-- 未来可以添加模型选择、工具按钮等 -->
          </div>
          
          <!-- 右侧控制按钮 -->
          <div class="right-controls">
            <!-- 停止按钮 (处理中时显示) -->
            <Tooltip
              v-if="isProcessing"
              content="停止请求"
              placement="top-end"
            >
              <button
                @click="handleStop"
                class="stop-btn"
              >
                <span>停止</span>
                <i class="ri-stop-fill"></i>
              </button>
            </Tooltip>

            <!-- 发送按钮 (非处理中时显示) -->
            <Tooltip
              v-else
              :content="canSend ? '点击发送消息' : '请输入内容'"
              :shortcut="canSend ? 'Ctrl + Enter' : ''"
              :disabled="!canSend"
              placement="top-end"
            >
              <button
                @click="handleSend"
                :disabled="!canSend"
                :class="['send-btn', { disabled: !canSend }]"
              >
                <div v-if="!isLoading">
                  <span>发送</span>
                  <i class="ri-send-plane-line"></i>
                </div>
                <div v-else class="loading-spinner">
                  <div class="spinner"></div>
                </div>
              </button>
            </Tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import Tooltip from './Tooltip.vue';

// Props
interface Props {
  modelValue: string
  isLoading: boolean
  isProcessing?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isProcessing: false
})

// Emits
interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'send', content: string): void
  (e: 'stop'): void
}

const emit = defineEmits<Emits>()

// Refs
const textareaRef = ref<HTMLTextAreaElement>()

// 计算属性
const inputValue = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value)
})

const canSend = computed(() => {
  return !props.isLoading && inputValue.value.trim().length > 0
})

/**
 * 处理键盘事件
 */
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl+Enter 发送消息
  if (event.ctrlKey && event.key === 'Enter') {
    event.preventDefault()
    handleSend()
    return
  }

  // Enter 换行（默认行为，不阻止）
  // Shift+Enter 也是换行（默认行为）
}

/**
 * 发送消息
 */
const handleSend = () => {
  if (!canSend.value) return

  const content = inputValue.value.trim()
  if (content) {
    emit('send', content)
  }
}

/**
 * 停止请求
 */
const handleStop = () => {
  emit('stop')
}

/**
 * 自动调整文本框高度
 */
const adjustHeight = () => {
  nextTick(() => {
    const textarea = textareaRef.value
    if (textarea) {
      // 重置高度以获取正确的scrollHeight
      textarea.style.height = 'auto'

      // 计算新高度，最小一行高度（约24px），最大200px
      const lineHeight = 24 // 一行的基础高度
      const minHeight = lineHeight
      const maxHeight = 200
      const newHeight = Math.max(minHeight, Math.min(textarea.scrollHeight, maxHeight))

      textarea.style.height = `${newHeight}px`

      // 如果内容超过最大高度，显示滚动条
      if (textarea.scrollHeight > maxHeight) {
        textarea.style.overflowY = 'auto'
      } else {
        textarea.style.overflowY = 'hidden'
      }
    }
  })
}

/**
 * 监听输入值变化，自动调整高度
 */
watch(() => props.modelValue, () => {
  adjustHeight()
})

/**
 * 聚焦输入框
 */
const focus = () => {
  nextTick(() => {
    textareaRef.value?.focus()
  })
}

/**
 * 组件挂载时初始化高度
 */
onMounted(() => {
  adjustHeight()
})

// 暴露方法给父组件
defineExpose({
  focus
})
</script>

<style scoped>
/* 输入容器 - 参考老版本样式 */
.input-container {
  padding: 10px;
  border-top: 1px solid var(--vscode-panel-border);
  background-color: var(--vscode-panel-background);
  display: flex;
  flex-direction: column;
  position: relative;
}

.textarea-container {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

.textarea-wrapper {
  flex: 1;
  background-color: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  border-radius: 6px;
  overflow: hidden;
}

.textarea-wrapper:focus-within {
  border-color: var(--vscode-focusBorder);
}

/* 输入框样式 - 参考老版本 */
.input-field {
  width: 100%;
  box-sizing: border-box;
  background-color: transparent;
  color: var(--vscode-input-foreground);
  border: none;
  padding: 8px 12px;
  outline: none;
  font-family: var(--vscode-editor-font-family);
  font-size: var(--vscode-font-size, 13px);
  min-height: auto;
  line-height: 1.4;
  overflow-y: hidden;
  resize: none;
}

.input-field:focus {
  border: none;
  outline: none;
}

.input-field::placeholder {
  color: var(--vscode-input-placeholderForeground);
  border: none;
  outline: none;
}

.input-field:disabled {
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
}

.input-field:disabled::placeholder {
  color: rgba(0, 0, 0, 0.25);
}

/* 控制栏样式 - 参考老版本 */
.input-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 2px 4px;
  border-top: 1px solid var(--vscode-panel-border);
  background-color: var(--vscode-input-background);
}

.left-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.right-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 发送按钮样式 - 参考老版本 */
.send-btn {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: none;
  padding: 3px 7px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  font-weight: 500;
  transition: all 0.2s ease;
}

/* 停止按钮样式 */
.stop-btn {
  background-color: var(--vscode-errorForeground);
  color: white;
  border: none;
  padding: 3px 7px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.send-btn div {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.send-btn span {
  line-height: 1;
}

.send-btn i {
  font-size: 11px;
  line-height: 1;
}

.send-btn:hover:not(.disabled) {
  background-color: var(--vscode-button-hoverBackground);
}

.send-btn.disabled,
.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.stop-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.stop-btn span {
  line-height: 1;
}

.stop-btn i {
  font-size: 11px;
  line-height: 1;
}

.stop-btn:hover {
  background-color: rgba(255, 69, 58, 0.8);
}

.stop-btn:active {
  transform: scale(0.95);
}

/* 加载动画 */
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  width: 10px;
  height: 10px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-top: 1px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 暗色主题适配 */
.dark .input-field:disabled {
  color: rgba(255, 255, 255, 0.25);
}

.dark .input-field:disabled::placeholder {
  color: rgba(255, 255, 255, 0.25);
}
</style>
