/* 引入 Remixicon 图标库 */
@import url('https://cdn.jsdelivr.net/npm/remixicon@4.6.0/fonts/remixicon.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 图标基础样式 */
i[class*="ri-"] {
  font-style: normal;
  line-height: 1;
}

/* 按钮中的图标样式 */
button i[class*="ri-"] {
  font-size: 14px;
}

/* 小尺寸图标 */
.ri-icon-sm {
  font-size: 12px;
}

/* VSCode主题变量 */
:root {
  --vscode-font-family: var(--vscode-font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
  --vscode-font-size: var(--vscode-font-size, 13px);
  --vscode-font-weight: var(--vscode-font-weight, normal);
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--vscode-font-family);
  font-size: var(--vscode-font-size);
  font-weight: var(--vscode-font-weight);
  color: var(--vscode-foreground);
  background-color: var(--vscode-editor-background);
  overflow: hidden;
}

/* 全局滚动条美化样式 - 超强优先级设计 */
html ::-webkit-scrollbar,
body ::-webkit-scrollbar,
* ::-webkit-scrollbar,
div ::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
}

html ::-webkit-scrollbar-track,
body ::-webkit-scrollbar-track,
* ::-webkit-scrollbar-track,
div ::-webkit-scrollbar-track {
  background: rgba(30, 30, 30, 0.3) !important;
  border-radius: 3px !important;
  margin: 1px 0 !important;
}

html ::-webkit-scrollbar-thumb,
body ::-webkit-scrollbar-thumb,
* ::-webkit-scrollbar-thumb,
div ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg,
    rgba(100, 149, 237, 0.7),
    rgba(70, 130, 180, 0.6)
  ) !important;
  border-radius: 3px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

html ::-webkit-scrollbar-thumb:hover,
body ::-webkit-scrollbar-thumb:hover,
* ::-webkit-scrollbar-thumb:hover,
div ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg,
    rgba(100, 149, 237, 0.9),
    rgba(70, 130, 180, 0.8)
  ) !important;
  transform: scaleX(1.4) !important;
  box-shadow: 0 2px 8px rgba(100, 149, 237, 0.4) !important;
}

html ::-webkit-scrollbar-thumb:active,
body ::-webkit-scrollbar-thumb:active,
* ::-webkit-scrollbar-thumb:active,
div ::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg,
    rgba(100, 149, 237, 1),
    rgba(70, 130, 180, 0.9)
  ) !important;
  transform: scaleX(1.2) !important;
}

/* 暗色主题适配 */
.dark ::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05) !important;
}

.dark ::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.18),
    rgba(255, 255, 255, 0.12)
  ) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.28),
    rgba(255, 255, 255, 0.22)
  ) !important;
}

/* VSCode字体大小工具类 */
.vscode-font-size {
  font-size: var(--vscode-font-size, 13px);
}

.vscode-font-size-sm {
  font-size: calc(var(--vscode-font-size, 13px) * 0.925); /* 约12px */
}

.vscode-font-size-xs {
  font-size: calc(var(--vscode-font-size, 13px) * 0.8); /* 约10.4px */
}

/* 自定义组件样式 */
@layer components {
  .chat-container {
    @apply h-screen flex flex-col;
    background-color: var(--vscode-editor-background);
  }

  .message-list {
    @apply flex-1 overflow-y-auto p-4 space-y-4;
  }

  .message-item {
    @apply animate-slide-up;
  }

  .message-user {
    @apply bg-transparent p-2 pb-0 rounded-lg ml-auto max-w-[80%];
    border: 2px solid rgba(59, 130, 246, 0.7);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
  }

  .message-assistant {
    @apply bg-transparent p-2 pb-0 rounded-lg mr-auto max-w-full;
    border: 2px solid rgba(34, 197, 94, 0.7);
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.15);
  }

  .input-container {
    padding: 16px;
    background-color: var(--vscode-input-background);
    border-top: 1px solid var(--vscode-panel-border);
  }

  .btn-primary {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    border: none;
    cursor: pointer;
    background: linear-gradient(135deg, #1677ff 0%, #0958d9 100%);
    color: #ffffff;
    box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2);
    font-size: 14px;
    font-weight: 500;
  }

  .btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #4096ff 0%, #1677ff 100%);
    box-shadow: 0 4px 8px rgba(22, 119, 255, 0.3);
    transform: translateY(-1px);
  }

  .btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2);
  }

  .btn-primary:disabled {
    background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
    color: rgba(0, 0, 0, 0.25);
    box-shadow: none;
    cursor: not-allowed;
    transform: none;
  }

  .btn-secondary {
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 500;
    border: 1px solid var(--vscode-input-border);
    transition: all 0.2s ease;
    outline: none;
    cursor: pointer;
    background-color: var(--vscode-input-background);
    border-color: var(--vscode-input-border);
    color: var(--vscode-input-foreground);
  }

  .btn-secondary:hover {
    background-color: var(--vscode-list-hoverBackground);
  }
}

/* 引入 Markdown 样式 */
@import './markdown.css';
