/* Markdown 样式文件 */

/* 代码块容器样式 */
.code-block-container {
  margin: 12px 0;
  border: 1px solid var(--vscode-panel-border);
  border-radius: 6px;
  background-color: var(--vscode-textCodeBlock-background);
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.code-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--vscode-editor-background);
  border-bottom: 1px solid var(--vscode-panel-border);
  font-size: 11px;
}

.code-block-language {
  color: var(--vscode-descriptionForeground);
  font-family: var(--vscode-editor-font-family);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.code-copy-btn {
  background: none;
  border: none;
  color: var(--vscode-descriptionForeground);
  cursor: pointer;
  padding: 4px 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.code-copy-btn:hover {
  background-color: var(--vscode-list-hoverBackground);
  opacity: 1;
}

.code-copy-btn.copy-success {
  color: var(--vscode-terminal-ansiGreen);
}

.code-copy-btn.copy-error {
  color: var(--vscode-errorForeground);
}

/* 代码块样式 */
.code-block {
  margin: 0;
  padding: 16px;
  background: transparent;
  border: none;
  border-radius: 0;
  font-family: var(--vscode-editor-font-family);
  font-size: 13px;
  line-height: 1.5;
  overflow-x: auto;
  white-space: pre;
  color: var(--vscode-editor-foreground);
}

.code-block code {
  background: none;
  border: none;
  padding: 0;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
}

/* 行内代码样式 */
.inline-code {
  background-color: var(--vscode-textCodeBlock-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 3px;
  padding: 2px 6px;
  font-family: var(--vscode-editor-font-family);
  font-size: 0.9em;
  color: var(--vscode-editor-foreground);
}

/* Highlight.js 主题适配 VSCode 暗色主题 */
.hljs {
  background: transparent !important;
  color: var(--vscode-editor-foreground) !important;
}

/* 关键字 */
.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-section,
.hljs-link {
  color: #569cd6 !important; /* VSCode 蓝色 */
}

/* 字符串 */
.hljs-string {
  color: #ce9178 !important; /* VSCode 橙色 */
}

/* 数字 */
.hljs-number {
  color: #b5cea8 !important; /* VSCode 浅绿色 */
}

/* 注释 */
.hljs-comment {
  color: #6a9955 !important; /* VSCode 绿色 */
  font-style: italic;
}

/* 函数名 */
.hljs-title,
.hljs-function .hljs-title {
  color: #dcdcaa !important; /* VSCode 黄色 */
}

/* 变量、属性 */
.hljs-variable,
.hljs-attr,
.hljs-property {
  color: #9cdcfe !important; /* VSCode 浅蓝色 */
}

/* 类型 */
.hljs-type,
.hljs-class .hljs-title {
  color: #4ec9b0 !important; /* VSCode 青色 */
}

/* 标签 */
.hljs-tag {
  color: #569cd6 !important;
}

/* 属性名 */
.hljs-name,
.hljs-attribute {
  color: #92c5f8 !important;
}

/* 符号、操作符 */
.hljs-symbol,
.hljs-operator,
.hljs-punctuation {
  color: var(--vscode-editor-foreground) !important;
}

/* 内置函数 */
.hljs-built_in {
  color: #4fc1ff !important;
}

/* 正则表达式 */
.hljs-regexp {
  color: #d16969 !important;
}

/* 元数据 */
.hljs-meta {
  color: #569cd6 !important;
}

/* 删除线 */
.hljs-deletion {
  background-color: rgba(255, 0, 0, 0.1);
  color: #ff6b6b !important;
}

/* 添加线 */
.hljs-addition {
  background-color: rgba(0, 255, 0, 0.1);
  color: #51cf66 !important;
}

/* 强调 */
.hljs-emphasis {
  font-style: italic;
}

/* 加粗 */
.hljs-strong {
  font-weight: bold;
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
  margin: 12px 0;
  border: 1px solid var(--vscode-panel-border);
  border-radius: 6px;
}

.markdown-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--vscode-editor-background);
}

.markdown-table th,
.markdown-table td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.markdown-table th {
  background-color: var(--vscode-textCodeBlock-background);
  font-weight: 600;
  color: var(--vscode-editor-foreground);
}

.markdown-table tr:hover {
  background-color: var(--vscode-list-hoverBackground);
}

/* 引用块样式 */
.markdown-content blockquote {
  margin: 12px 0;
  padding: 8px 16px;
  border-left: 4px solid var(--vscode-textLink-foreground);
  background-color: var(--vscode-textCodeBlock-background);
  border-radius: 0 4px 4px 0;
}

.markdown-content blockquote p {
  margin: 0;
  color: var(--vscode-descriptionForeground);
}

/* 列表样式 */
.markdown-content ul,
.markdown-content ol {
  margin: 8px 0;
  padding-left: 24px;
}

.markdown-content li {
  margin: 4px 0;
  line-height: 1.5;
}

/* 链接样式 */
.markdown-content a {
  color: var(--vscode-textLink-foreground);
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
  color: var(--vscode-textLink-activeForeground);
}

/* 分隔线样式 */
.markdown-content hr {
  border: none;
  border-top: 1px solid var(--vscode-panel-border);
  margin: 16px 0;
}
