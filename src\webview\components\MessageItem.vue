<template>
  <!-- 工具调用消息 -->
  <ToolUseMessage
    v-if="message.type === 'tool'"
    :tool-info="message.toolInfo || ''"
    :tool-input="message.toolInput"
    :raw-input="message.rawInput"
    :tool-name="message.toolName || ''"
    :tool-use-id="message.toolUseId"
    :timestamp="message.timestamp"
    :tool-result="toolResult"
    :is-loading="isToolLoading"
  />

  <!-- 思考过程消息 -->
  <ThinkingMessage
    v-else-if="message.type === 'thinking'"
    :content="message.content"
    :timestamp="message.timestamp"
    :is-active="message.isStreaming"
  />

  <!-- 系统消息 -->
  <div v-else-if="message.type === 'system'" class="system-message">
    <div class="system-content">
      <span class="system-icon">ℹ️</span>
      <span class="system-text">{{ message.content }}</span>
    </div>
  </div>

  <!-- 错误消息 -->
  <div v-else-if="message.type === 'error'" class="error-message">
    <div class="error-content">
      <span class="error-icon">⚠️</span>
      <span class="error-text">{{ message.content }}</span>
    </div>
  </div>

  <!-- 普通用户/助手消息 -->
  <div
    v-else
    :class="[
      'message-item flex',
      message.type === 'user' ? 'justify-end' : 'justify-start',
    ]"
  >
    <div
      :class="[
        'message-content',
        message.type === 'user' ? 'message-user' : 'message-assistant w-full',
      ]"
    >
      <!-- 消息头部 -->
      <div
        :class="[
          'flex items-center mb-1',
          message.type === 'user' ? 'justify-end' : 'justify-start',
        ]"
      >
        <div
          v-if="message.type === 'assistant'"
          class="flex items-center space-x-1.5"
        >
          <div
            class="w-4 h-4 rounded-full bg-green-500 text-white flex items-center justify-center text-[10px] font-bold"
          >
            C
          </div>
          <span class="vscode-font-size-sm font-medium">Claude</span>
          <span class="vscode-font-size-xs opacity-50">{{
            formatTime(message.timestamp)
          }}</span>
        </div>

        <div v-else class="flex items-center space-x-1.5">
          <span class="vscode-font-size-xs opacity-50">{{
            formatTime(message.timestamp)
          }}</span>
          <span class="vscode-font-size-sm font-medium">您</span>
          <div
            class="w-4 h-4 rounded-full bg-blue-500 text-white flex items-center justify-center text-[10px] font-bold"
          >
            U
          </div>
        </div>
      </div>

      <!-- 消息内容 -->
      <div class="message-text">
        <div
          v-if="message.content"
          class="markdown-content"
          v-html="formattedContent"
        />

        <!-- 流式输入指示器 -->
        <div
          v-if="message.isStreaming"
          class="flex items-center space-x-1 mt-1"
        >
          <div class="flex space-x-1">
            <div
              class="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"
            ></div>
            <div
              class="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"
              style="animation-delay: 0.2s"
            ></div>
            <div
              class="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"
              style="animation-delay: 0.4s"
            ></div>
          </div>
          <span class="vscode-font-size-xs opacity-50">正在输入...</span>
        </div>

        <!-- 流式光标效果 -->
        <span
          v-if="message.isStreaming && message.content"
          class="inline-block w-0.5 h-4 bg-green-500 animate-pulse ml-0.5"
        ></span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import type { Message, ToolResultData } from "../../types";
import { parseMarkdown, setupCodeCopyFunction } from "../utils/markdown";
import ThinkingMessage from "./ThinkingMessage.vue";
import ToolUseMessage from "./ToolUseMessage.vue";

// Props
interface Props {
  message: Message;
}

const props = defineProps<Props>();

/**
 * 工具结果数据
 */
const toolResult = computed((): ToolResultData | undefined => {
  if (props.message.type !== "tool" || !props.message.content) return undefined;

  return {
    content: props.message.content,
    isError: props.message.isError || false,
    toolUseId: props.message.toolUseId || "",
    toolName: props.message.toolName || "",
    hidden: props.message.hidden,
  };
});

/**
 * 工具是否正在加载
 */
const isToolLoading = computed(() => {
  return (
    props.message.type === "tool" &&
    props.message.toolUseId &&
    !props.message.content &&
    !props.message.isError
  );
});

/**
 * 格式化时间显示
 */
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit"
  });
};

/**
 * 格式化消息内容
 * 使用markdown-it进行完整的Markdown渲染
 */
const formattedContent = computed(() => {
  if (!props.message.content) {
    return "";
  }

  return parseMarkdown(props.message.content);
});

/**
 * 组件挂载时设置复制功能
 */
onMounted(() => {
  setupCodeCopyFunction();
});
</script>

<style scoped>
.message-content {
  @apply transition-all duration-200 backdrop-blur-sm;
  background: rgba(var(--vscode-editor-background-rgb, 30, 30, 30), 0.1);
}

.message-user {
  border: 2px solid rgba(59, 130, 246, 0.8) !important;
  box-shadow: 0 2px 12px rgba(59, 130, 246, 0.18) !important;
  backdrop-filter: blur(8px);
}

.message-assistant {
  border: 2px solid rgba(34, 197, 94, 0.8) !important;
  box-shadow: 0 2px 12px rgba(34, 197, 94, 0.18) !important;
  backdrop-filter: blur(8px);
}

.message-text {
  @apply leading-relaxed;
}

/* 强制覆盖Markdown样式 */
.message-content :deep(.markdown-content h1) {
  font-size: calc(var(--vscode-font-size, 13px) * 1.20) !important;
  font-weight: 600 !important;
  margin: 0.5rem 0 0.25rem 0 !important;
  line-height: 1.25 !important;
  border-bottom: 1px solid var(--vscode-panel-border) !important;
  padding-bottom: 0.25rem !important;
}

.message-content :deep(.markdown-content h2) {
  font-size: calc(var(--vscode-font-size, 13px) * 1.10) !important;
  font-weight: 600 !important;
  margin: 0.5rem 0 0.25rem 0 !important;
  line-height: 1.25 !important;
  border-bottom: 1px solid var(--vscode-panel-border) !important;
  padding-bottom: 0.125rem !important;
}

.message-content :deep(.markdown-content h3) {
  font-size: calc(var(--vscode-font-size, 13px) * 1.05) !important;
  font-weight: 600 !important;
  margin: 0.5rem 0 0.25rem 0 !important;
  line-height: 1.25 !important;
}

.message-content :deep(.markdown-content p) {
  font-size: var(--vscode-font-size, 13px) !important;
  margin-bottom: 0.5rem !important;
  line-height: 1.625 !important;
}

.message-content :deep(.markdown-content ul),
.message-content :deep(.markdown-content ol) {
  font-size: var(--vscode-font-size, 13px) !important;
  margin-bottom: 0.5rem !important;
  padding-left: 1rem !important;
}

/* 系统消息样式 */
.system-message {
  margin: 8px 0;
  padding: 8px 12px;
  background: var(--vscode-textCodeBlock-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 6px;
  border-left: 3px solid var(--vscode-progressBar-background);
}

.system-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--vscode-foreground);
}

.system-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.system-text {
  opacity: 0.9;
}

/* 错误消息样式 */
.error-message {
  margin: 8px 0;
  padding: 8px 12px;
  background: rgba(255, 69, 58, 0.1);
  border: 1px solid var(--vscode-errorForeground);
  border-radius: 6px;
  border-left: 3px solid var(--vscode-errorForeground);
}

.error-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--vscode-errorForeground);
}

.error-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.error-text {
  opacity: 0.9;
}

/* 代码块容器样式 */
.message-content :deep(.code-block-container) {
  margin: 12px 0;
  border: 1px solid var(--vscode-panel-border);
  border-radius: 6px;
  background-color: var(--vscode-textCodeBlock-background);
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-content :deep(.code-block-header) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--vscode-editor-background);
  border-bottom: 1px solid var(--vscode-panel-border);
  font-size: 11px;
}

.message-content :deep(.code-block-language) {
  color: var(--vscode-descriptionForeground);
  font-family: var(--vscode-editor-font-family);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.message-content :deep(.code-copy-btn) {
  background: none;
  border: none;
  color: var(--vscode-descriptionForeground);
  cursor: pointer;
  padding: 4px 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.message-content :deep(.code-copy-btn:hover) {
  background-color: var(--vscode-list-hoverBackground);
  opacity: 1;
}

.message-content :deep(.code-copy-btn.copy-success) {
  color: var(--vscode-terminal-ansiGreen);
}

.message-content :deep(.code-copy-btn.copy-error) {
  color: var(--vscode-errorForeground);
}

/* 代码块样式 */
.message-content :deep(.code-block) {
  margin: 0;
  padding: 16px;
  background: transparent;
  border: none;
  border-radius: 0;
  font-family: var(--vscode-editor-font-family);
  font-size: 13px;
  line-height: 1.5;
  overflow-x: auto;
  white-space: pre;
  color: var(--vscode-editor-foreground);
}

.message-content :deep(.code-block code) {
  background: none;
  border: none;
  padding: 0;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
}

/* 行内代码样式 */
.message-content :deep(.inline-code) {
  background-color: var(--vscode-textCodeBlock-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 3px;
  padding: 2px 6px;
  font-family: var(--vscode-editor-font-family);
  font-size: 0.9em;
  color: var(--vscode-editor-foreground);
}

/* Highlight.js 主题适配 VSCode 暗色主题 */
.message-content :deep(.hljs) {
  background: transparent !important;
  color: var(--vscode-editor-foreground) !important;
}

/* 关键字 */
.message-content :deep(.hljs-keyword),
.message-content :deep(.hljs-selector-tag),
.message-content :deep(.hljs-literal),
.message-content :deep(.hljs-section),
.message-content :deep(.hljs-link) {
  color: #569cd6 !important; /* VSCode 蓝色 */
}

/* 字符串 */
.message-content :deep(.hljs-string) {
  color: #ce9178 !important; /* VSCode 橙色 */
}

/* 数字 */
.message-content :deep(.hljs-number) {
  color: #b5cea8 !important; /* VSCode 浅绿色 */
}

/* 注释 */
.message-content :deep(.hljs-comment) {
  color: #6a9955 !important; /* VSCode 绿色 */
  font-style: italic;
}

/* 函数名 */
.message-content :deep(.hljs-title),
.message-content :deep(.hljs-function .hljs-title) {
  color: #dcdcaa !important; /* VSCode 黄色 */
}

/* 变量、属性 */
.message-content :deep(.hljs-variable),
.message-content :deep(.hljs-attr),
.message-content :deep(.hljs-property) {
  color: #9cdcfe !important; /* VSCode 浅蓝色 */
}

/* 类型 */
.message-content :deep(.hljs-type),
.message-content :deep(.hljs-class .hljs-title) {
  color: #4ec9b0 !important; /* VSCode 青色 */
}

/* 标签 */
.message-content :deep(.hljs-tag) {
  color: #569cd6 !important;
}

/* 属性名 */
.message-content :deep(.hljs-name),
.message-content :deep(.hljs-attribute) {
  color: #92c5f8 !important;
}

/* 符号、操作符 */
.message-content :deep(.hljs-symbol),
.message-content :deep(.hljs-operator),
.message-content :deep(.hljs-punctuation) {
  color: var(--vscode-editor-foreground) !important;
}

/* 内置函数 */
.message-content :deep(.hljs-built_in) {
  color: #4fc1ff !important;
}

/* 正则表达式 */
.message-content :deep(.hljs-regexp) {
  color: #d16969 !important;
}

/* 元数据 */
.message-content :deep(.hljs-meta) {
  color: #569cd6 !important;
}

/* 删除线 */
.message-content :deep(.hljs-deletion) {
  background-color: rgba(255, 0, 0, 0.1);
  color: #ff6b6b !important;
}

/* 添加线 */
.message-content :deep(.hljs-addition) {
  background-color: rgba(0, 255, 0, 0.1);
  color: #51cf66 !important;
}

/* 强调 */
.message-content :deep(.hljs-emphasis) {
  font-style: italic;
}

/* 加粗 */
.message-content :deep(.hljs-strong) {
  font-weight: bold;
}
</style>
